services:
  # HTTPS Reverse Proxy
  nginx:
    build:
      context: ./docker/nginx
      dockerfile: Dockerfile
    ports:
      - '80:80'
      - '443:443'
    depends_on:
      payload:
        condition: service_healthy
    volumes:
      - nginx-certs:/etc/nginx/certs
    healthcheck:
      test: ["CMD", "wget", "-q", "-O", "-", "https://localhost/api/health", "--no-check-certificate"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Main application service
  payload:
    image: node:20-alpine
    expose:
      - '3000'
    ports:
      - '9229:9229'
    volumes:
      # Mount source code but exclude node_modules directory
      - .:/home/<USER>/app:cached
      # Use named volume for node_modules
      - node_modules:/home/<USER>/app/node_modules
    working_dir: /home/<USER>/app/
    command: sh -c "npm install -g pnpm && pnpm install && pnpm run dev:container"
    depends_on:
      mongo:
        condition: service_healthy
    env_file:
      - .env
    environment:
      # Ensure app knows it's running in docker
      - DOCKER_ENVIRONMENT=true
      # Force terminal to support colors
      - FORCE_COLOR=1
    develop:
      watch:
        - path: package.json
          action: rebuild
    # Enable container to have proper terminal support
    tty: true
    stdin_open: true
    # Healthcheck ensures container restarts if app crashes
    healthcheck:
      test: ["CMD", "wget", "-q", "-O", "-", "http://localhost:3000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s

  # Database service
  mongo:
    image: mongo:latest
    ports:
      - '27017:27017'
    command:
      - --storageEngine=wiredTiger
    volumes:
      - db-data:/data/db
      # Mount database init scripts
      - ./docker/mongo/init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s
    environment:
      - MONGO_INITDB_DATABASE=senergy
    # Uncomment these for admin password (recommended for production)
    # - MONGO_INITDB_ROOT_USERNAME=admin
    # - MONGO_INITDB_ROOT_PASSWORD=password

  # Database management UI
  mongo-express:
    image: mongo-express
    ports:
      - '8081:8081'
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      # Uncomment these if you set admin credentials for MongoDB
      # - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      # - ME_CONFIG_MONGODB_ADMINPASSWORD=password
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=password
    depends_on:
      mongo:
        condition: service_healthy
    # Healthcheck ensures container restarts if UI crashes
    healthcheck:
      test: ["CMD", "wget", "-q", "-O", "-", "http://localhost:8081/"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s

volumes:
  db-data:
  node_modules:
  nginx-certs:
