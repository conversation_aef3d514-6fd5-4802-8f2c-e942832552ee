#!/bin/bash

# Setup script for HTTPS/HTTP development environment
# Usage:
#   ./scripts/setup-https.sh          # Enable HTTPS (default)
#   ./scripts/setup-https.sh https    # Enable HTTPS
#   ./scripts/setup-https.sh http     # Enable HTTP

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default to HTTPS if no argument provided
ENABLE_HTTPS=${1:-"https"}

echo -e "${BLUE}🔒 Setting up local development environment${NC}"
echo ""

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  No .env file found. Creating from .env.example...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ Created .env file${NC}"
else
    echo -e "${GREEN}✅ Found existing .env file${NC}"
fi

# Function to update environment variables
update_env_var() {
    local var_name=$1
    local var_value=$2
    local env_file=".env"

    if grep -q "^${var_name}=" "$env_file"; then
        # Variable exists, update it
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s|^${var_name}=.*|${var_name}=\"${var_value}\"|" "$env_file"
        else
            # Linux
            sed -i "s|^${var_name}=.*|${var_name}=\"${var_value}\"|" "$env_file"
        fi
    else
        # Variable doesn't exist, add it
        echo "${var_name}=\"${var_value}\"" >> "$env_file"
    fi
}

# Check if user wants HTTPS or HTTP
if [[ $ENABLE_HTTPS == "https" ]]; then
    echo -e "${BLUE}🔧 Configuring environment for HTTPS...${NC}"

    # Update environment variables to use HTTPS
    update_env_var "SITE_URL" "https://localhost"
    update_env_var "NEXT_PUBLIC_SERVER_URL" "https://localhost"
    update_env_var "BETTER_AUTH_URL" "https://localhost"
    update_env_var "PAYLOAD_PUBLIC_SERVER_URL" "https://localhost"
    update_env_var "PAYLOAD_PUBLIC_SITE_URL" "https://localhost"

    echo -e "${GREEN}✅ Environment configured for HTTPS${NC}"
    echo ""
    echo -e "${BLUE}📋 Next steps:${NC}"
    echo -e "1. Run: ${GREEN}pnpm dev${NC} (or ${GREEN}docker compose up${NC})"
    echo -e "2. Wait for the containers to start"
    echo -e "3. Open: ${GREEN}https://localhost${NC}"
    echo -e "4. Accept the self-signed certificate warning in your browser"
    echo ""
    echo -e "${YELLOW}⚠️  Note: You'll see a security warning because we're using a self-signed certificate.${NC}"
    echo -e "${YELLOW}   This is normal for local development. Click 'Advanced' and 'Proceed to localhost'.${NC}"

else
    echo -e "${BLUE}🔧 Configuring environment for HTTP...${NC}"

    # Update environment variables to use HTTP
    update_env_var "SITE_URL" "http://localhost:3000"
    update_env_var "NEXT_PUBLIC_SERVER_URL" "http://localhost:3000"
    update_env_var "BETTER_AUTH_URL" "http://localhost:3000"
    update_env_var "PAYLOAD_PUBLIC_SERVER_URL" "http://localhost:3000"
    update_env_var "PAYLOAD_PUBLIC_SITE_URL" "http://localhost:3000"

    echo -e "${GREEN}✅ Environment configured for HTTP${NC}"
    echo ""
    echo -e "${BLUE}📋 Next steps:${NC}"
    echo -e "1. Run: ${GREEN}pnpm dev${NC} (or ${GREEN}docker compose up${NC})"
    echo -e "2. Open: ${GREEN}http://localhost:3000${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Setup complete!${NC}"
