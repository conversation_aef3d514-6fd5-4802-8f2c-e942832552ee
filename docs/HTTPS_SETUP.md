# HTTPS Setup for Local Development

This document explains how to use HTTPS in your local Docker development environment.

## Quick Start

### Enable HTTPS (Recommended)
```bash
pnpm dev:https
```

### Enable HTTP (Alternative)
```bash
pnpm dev:http
```

## What's Included

The HTTPS setup includes:

1. **NGINX Reverse Proxy**: Handles SSL termination and proxies requests to your Next.js app
2. **Self-Signed SSL Certificates**: Automatically generated for `localhost`
3. **HTTP to HTTPS Redirect**: All HTTP requests are redirected to HTTPS
4. **Modern Security Headers**: HSTS, X-Frame-Options, etc.
5. **WebSocket Support**: For Next.js hot reload functionality

## Architecture

```
Browser (https://localhost) 
    ↓
NGINX (Port 443) - SSL Termination
    ↓
Next.js App (Port 3000) - HTTP internally
```

## File Structure

```
docker/
├── nginx/
│   ├── Dockerfile          # Custom NGINX image with OpenSSL
│   ├── nginx.conf          # NGINX configuration
│   ├── generate-certs.sh   # Certificate generation script
│   └── README.md           # NGINX-specific documentation
└── mongo/                  # MongoDB configuration

scripts/
└── setup-https.sh          # Environment setup script
```

## Environment Variables

The setup automatically configures these environment variables:

### HTTPS Mode
```bash
SITE_URL="https://localhost"
NEXT_PUBLIC_SERVER_URL="https://localhost"
BETTER_AUTH_URL="https://localhost"
PAYLOAD_PUBLIC_SERVER_URL="https://localhost"
PAYLOAD_PUBLIC_SITE_URL="https://localhost"
```

### HTTP Mode
```bash
SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SERVER_URL="http://localhost:3000"
BETTER_AUTH_URL="http://localhost:3000"
PAYLOAD_PUBLIC_SERVER_URL="http://localhost:3000"
PAYLOAD_PUBLIC_SITE_URL="http://localhost:3000"
```

## SSL Certificate Details

- **Type**: Self-signed X.509 certificate
- **Validity**: 365 days
- **Key Size**: 2048-bit RSA
- **Subject**: CN=localhost
- **SAN**: localhost, *.localhost, 127.0.0.1, ::1
- **Storage**: Docker volume `nginx-certs`

## Manual Setup

If you prefer manual setup:

1. **Configure Environment**:
   ```bash
   bash scripts/setup-https.sh https  # or 'http'
   ```

2. **Start Services**:
   ```bash
   docker compose up
   ```

3. **Access Application**:
   - HTTPS: https://localhost
   - HTTP: http://localhost:3000

## Browser Security Warning

When using HTTPS with self-signed certificates, you'll see a security warning. This is normal and expected for local development.

**To proceed**:
1. Click "Advanced" or "Show details"
2. Click "Proceed to localhost (unsafe)" or similar
3. The warning won't appear again for this session

## Troubleshooting

### Certificate Issues
```bash
# Check if certificates exist
docker compose exec nginx ls -la /etc/nginx/certs/

# Regenerate certificates
docker compose down
docker volume rm senergy-v5_nginx-certs
docker compose up
```

### NGINX Issues
```bash
# Test NGINX configuration
docker compose exec nginx nginx -t

# View NGINX logs
docker compose logs nginx

# Restart NGINX
docker compose restart nginx
```

### Port Conflicts
If ports 80 or 443 are in use:

**macOS**: Stop Apache if running
```bash
sudo apachectl stop
```

**Check what's using the ports**:
```bash
lsof -i :80
lsof -i :443
```

### Connection Refused
1. Ensure all containers are healthy: `docker compose ps`
2. Check if the app is responding: `curl -k https://localhost/api/health`
3. Verify environment variables are correct

## Security Considerations

### Development Only
- Self-signed certificates are for development only
- Never use these certificates in production
- The setup includes security headers appropriate for development

### Production Deployment
For production, you'll need:
- Valid SSL certificates from a Certificate Authority
- Proper domain configuration
- Production-grade security headers
- Proper secret management

## Performance Notes

- NGINX adds minimal overhead for local development
- SSL/TLS processing is handled by NGINX, not Node.js
- Hot reload and debugging work normally through the proxy
- WebSocket connections are properly proxied

## Switching Modes

You can switch between HTTPS and HTTP at any time:

```bash
# Switch to HTTPS
bash scripts/setup-https.sh https
docker compose up

# Switch to HTTP  
bash scripts/setup-https.sh http
docker compose up
```

The setup script will update your `.env` file automatically.
