# Developer Guide

## Getting Started

This project uses Docker Compose for local development to ensure a consistent development environment across all machines.

### Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker)
- [Node.js](https://nodejs.org/) (for running commands outside containers if needed)
- [pnpm](https://pnpm.io/installation) (package manager)

### Development Workflow

#### Starting the Development Environment

##### Option 1: HTTPS Development (Recommended)

For a production-like environment with HTTPS:

```bash
# Setup HTTPS and start development environment
pnpm dev:https
```

This will:
1. Configure your environment for HTTPS
2. Generate self-signed SSL certificates
3. Start all services with HTTPS enabled
4. Your app will be available at: https://localhost

**Note**: You'll see a browser security warning for the self-signed certificate. This is normal for local development. Click "Advanced" and "Proceed to localhost" to continue.

##### Option 2: HTTP Development

For simple HTTP development:

```bash
# Start development environment with HTTP
pnpm dev
```

Your app will be available at: http://localhost:3000

##### Option 3: Manual HTTPS Setup

If you want to configure HTTPS manually:

```bash
# Run the HTTPS setup script
bash scripts/setup-https.sh

# Then start the development environment
pnpm dev
```

#### Service URLs

When running with HTTPS:
- **Main Application**: https://localhost
- **MongoDB Express**: http://localhost:8081 (admin/password)
- **MongoDB**: localhost:27017

When running with HTTP:
- **Main Application**: http://localhost:3000
- **MongoDB Express**: http://localhost:8081 (admin/password)
- **MongoDB**: localhost:27017

#### SSL Certificate Information

The HTTPS setup uses self-signed certificates that are automatically generated. These certificates:
- Are valid for `localhost` and `127.0.0.1`
- Are stored in a Docker volume (`nginx-certs`)
- Are regenerated automatically if missing
- Include Subject Alternative Names (SAN) for better compatibility

#### Switching Between HTTP and HTTPS

To switch from HTTPS to HTTP or vice versa:

1. Stop the current development environment:
   ```bash
   docker compose down
   ```

2. Run the setup script and choose your preferred option:
   ```bash
   bash scripts/setup-https.sh
   ```

3. Start the development environment:
   ```bash
   pnpm dev
   ```

#### Troubleshooting HTTPS

**Certificate Warnings**:
- Browser warnings are expected with self-signed certificates
- Click "Advanced" → "Proceed to localhost" to continue

**Connection Issues**:
- Ensure all containers are healthy: `docker compose ps`
- Check nginx logs: `docker compose logs nginx`
- Verify certificates exist: `docker compose exec nginx ls -la /etc/nginx/certs/`

**Port Conflicts**:
- HTTPS uses ports 80 and 443
- Make sure these ports aren't used by other applications
- On macOS, you might need to stop Apache: `sudo apachectl stop`
