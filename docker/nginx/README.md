# NGINX HTTPS Proxy

This directory contains the NGINX configuration for enabling HTTPS in the local development environment.

## Files

- **`nginx.conf`**: Main NGINX configuration with HTTPS proxy settings
- **`Dockerfile`**: Custom NGINX image with OpenSSL for certificate generation
- **`generate-certs.sh`**: <PERSON>ript that generates self-signed SSL certificates and starts NGINX

## How it works

1. **Certificate Generation**: When the container starts, it automatically generates self-signed SSL certificates for `localhost`
2. **HTTPS Termination**: NGINX handles SSL/TLS termination and proxies requests to the Node.js application
3. **HTTP Redirect**: All HTTP requests are automatically redirected to HTTPS
4. **WebSocket Support**: Configured to support Next.js hot reload and WebSocket connections

## Certificate Details

The generated certificates include:
- **Common Name**: localhost
- **Subject Alternative Names**: localhost, *.localhost, 127.0.0.1, ::1
- **Validity**: 365 days
- **Key Size**: 2048-bit RSA

## Security Headers

The NGINX configuration includes modern security headers:
- `Strict-Transport-Security`: Forces HTTPS for future requests
- `X-Frame-Options`: Prevents clickjacking
- `X-Content-Type-Options`: Prevents MIME type sniffing
- `X-XSS-Protection`: Enables XSS filtering

## Customization

To customize the NGINX configuration:
1. Edit `nginx.conf` for proxy settings
2. Modify `generate-certs.sh` for certificate parameters
3. Update `Dockerfile` for additional packages or configuration

## Troubleshooting

**Certificates not generated**:
```bash
docker compose exec nginx ls -la /etc/nginx/certs/
```

**NGINX configuration test**:
```bash
docker compose exec nginx nginx -t
```

**View NGINX logs**:
```bash
docker compose logs nginx
```
