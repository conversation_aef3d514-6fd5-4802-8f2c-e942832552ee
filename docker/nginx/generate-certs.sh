#!/bin/bash

# Create certificates directory if it doesn't exist
mkdir -p /etc/nginx/certs

# Generate self-signed certificate for localhost
if [ ! -f /etc/nginx/certs/localhost.crt ] || [ ! -f /etc/nginx/certs/localhost.key ]; then
    echo "Generating self-signed SSL certificate for localhost..."
    
    # Create a config file for the certificate
    cat > /tmp/localhost.conf <<EOF
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = Development
L = Local
O = Development
OU = Development
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

    # Generate the certificate
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout /etc/nginx/certs/localhost.key \
        -out /etc/nginx/certs/localhost.crt \
        -config /tmp/localhost.conf \
        -extensions v3_req

    # Set proper permissions
    chmod 600 /etc/nginx/certs/localhost.key
    chmod 644 /etc/nginx/certs/localhost.crt
    
    echo "SSL certificate generated successfully!"
    echo "Certificate: /etc/nginx/certs/localhost.crt"
    echo "Private Key: /etc/nginx/certs/localhost.key"
else
    echo "SSL certificate already exists, skipping generation."
fi

# Start nginx
exec nginx -g "daemon off;"
