FROM nginx:alpine

# Install OpenSSL for certificate generation
RUN apk add --no-cache openssl

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy certificate generation script
COPY generate-certs.sh /usr/local/bin/generate-certs.sh
RUN chmod +x /usr/local/bin/generate-certs.sh

# Create directory for certificates
RUN mkdir -p /etc/nginx/certs

# Expose ports
EXPOSE 80 443

# Use the certificate generation script as entrypoint
CMD ["/usr/local/bin/generate-certs.sh"]
