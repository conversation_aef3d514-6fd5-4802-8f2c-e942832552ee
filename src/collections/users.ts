import type { CollectionConfig } from 'payload';
import { isAdmin } from '@/access/is-admin';
import { isAdminOrSelf } from '@/access/is-admin-or-self';
import { auth } from '@/auth/server';

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    //hidden: true,
  },
  access: {
    read: isAdminOrSelf,
    update: () => false,
    delete: () => false,
    create: () => false,
    admin: isAdmin,
  },
  auth: {
    disableLocalStrategy: true,
    removeTokenFromResponses: true,
    tokenExpiration: 0,
    useAPIKey: false,
    strategies: [
      {
        name: 'better-auth',
        // @ts-expect-error
        authenticate: async ({ headers }) => {
          const session = await auth.api.getSession({ headers });
          if (!session?.user) {
            return { user: null, error: 'Not authenticated' };
          }
          return { user: { ...session.user, collection: 'users' } };
        },
      },
    ],
  },
  endpoints: [
    {
      path: '/me',
      method: 'get',
      handler: async (req) => {
        const session = await auth.api.getSession({ headers: req.headers });
        if (!session) {
          return new Response(
            JSON.stringify({ message: 'Not authenticated' }),
            {
              status: 401,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        }
        return new Response(
          JSON.stringify({
            user: { ...session.user, collection: 'users' },
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      },
    },
    {
      path: '/logout',
      method: 'post',
      handler: async (req) => {
        await auth.api.signOut({ headers: req.headers });
        return new Response(JSON.stringify({ message: 'Logged out' }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });
      },
    },
    {
      path: '/waitlist',
      method: 'post',
      handler: async (req) => {
        try {
          // Parse request body
          const body = await req.json();
          const { email } = body;

          // Validate email
          if (!email || typeof email !== 'string') {
            return new Response(
              JSON.stringify({
                error: 'Email is required',
                message: 'Please provide a valid email address'
              }),
              {
                status: 400,
                headers: { 'Content-Type': 'application/json' },
              }
            );
          }

          // Basic email validation
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email)) {
            return new Response(
              JSON.stringify({
                error: 'Invalid email format',
                message: 'Please provide a valid email address'
              }),
              {
                status: 400,
                headers: { 'Content-Type': 'application/json' },
              }
            );
          }

          // Use Better Auth's magic link system to create/authenticate user
          // This automatically handles rate limiting (6 requests per 5 minutes)
          // and creates users with 'waitlisted' role by default
          const callbackURL = `${process.env.BETTER_AUTH_URL}/waitlisted`;

          await auth.api.signInMagicLink({
            body: JSON.stringify({
              email,
              callbackURL,
            }),
            headers: req.headers,
          });

          return new Response(
            JSON.stringify({
              success: true,
              message: 'Successfully joined the waitlist! Check your email for a magic link to access your account.'
            }),
            {
              status: 200,
              headers: { 'Content-Type': 'application/json' },
            }
          );

        } catch (error) {
          // Handle rate limiting and other Better Auth errors
          if (error instanceof Error) {
            // Check if it's a rate limit error
            if (error.message.includes('rate limit') || error.message.includes('too many')) {
              return new Response(
                JSON.stringify({
                  error: 'Rate limit exceeded',
                  message: 'Too many requests. Please try again in a few minutes.'
                }),
                {
                  status: 429,
                  headers: { 'Content-Type': 'application/json' },
                }
              );
            }
          }

          return new Response(
            JSON.stringify({
              error: 'Internal server error',
              message: 'Something went wrong. Please try again later.'
            }),
            {
              status: 500,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        }
      },
    },
  ],
  fields: [
    {
      name: 'name',
      label: 'Preferred Name',
      type: 'text',
      virtual: true,
    },
    {
      name: 'email',
      label: 'Email',
      type: 'text',
      required: true,
      virtual: true,
    },
    {
      name: 'emailVerified',
      label: 'Email Verified',
      type: 'checkbox',
      defaultValue: false,
      virtual: true,
    },
    {
      name: 'image',
      label: 'Image',
      type: 'text',
      virtual: true,
    },
    {
      name: 'role',
      label: 'Role',
      type: 'select',
      options: ['waitlisted', 'user', 'admin', 'superadmin'],
      defaultValue: 'waitlisted',
      virtual: true,
    },
    {
      name: 'banned',
      label: 'Banned',
      type: 'checkbox',
      defaultValue: false,
      virtual: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'banReason',
      label: 'Ban Reason',
      type: 'text',
      virtual: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'banExpires',
      label: 'Banned Until',
      type: 'date',
      virtual: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'profile',
      label: 'Profile',
      type: 'join',
      collection: 'profile',
      on: 'owner',
      hasMany: false,
    },
  ],
};
